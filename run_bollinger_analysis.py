#!/usr/bin/env python3
"""
Simple runner script for Bollinger Band Breakout Analysis
=========================================================

This script provides an easy way to run the comprehensive Bollinger Band
breakout analysis with customizable parameters.

Usage:
    python run_bollinger_analysis.py

The script will:
1. Load the CSV data from the output directory
2. Run comprehensive statistical and temporal analysis
3. Generate visualizations and reports
4. Save all results to the analysis_output directory

Author: Augment Agent
Date: 2025-08-10
"""

import sys
import os
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

try:
    from analysis.bollinger_band_breakout_analyzer import BollingerBreakoutAnalyzer, AnalysisConfig
except ImportError as e:
    print(f"Error importing analysis modules: {e}")
    print("Please ensure all required packages are installed:")
    print("pip install -r src/analysis/requirements.txt")
    sys.exit(1)


def main():
    """Main execution function with user-friendly interface"""
    print("=" * 80)
    print("BOLLINGER BAND BREAKOUT ANALYSIS")
    print("=" * 80)
    print("This analysis will examine BB_Above_Upper_Pct and BB_Below_Lower_Pct")
    print("breakout events in BTCUSDT 15-minute data with comprehensive")
    print("statistical, temporal, and risk-return analysis.")
    print()
    
    # Check if data file exists
    data_file = "output/BTCUSDT_15m_indicators_20250810_170018.csv"
    if not os.path.exists(data_file):
        print(f"ERROR: Data file not found: {data_file}")
        print("Please ensure the indicator calculator has been run first.")
        sys.exit(1)
    
    print(f"Data file found: {data_file}")
    
    # Configure analysis parameters
    config = AnalysisConfig(
        data_file=data_file,
        output_dir="analysis_output",
        min_breakout_threshold=0.01,  # Minimum 0.01% breakout to consider
        lookforward_periods=[1, 4, 12, 24, 48],  # 15min, 1h, 3h, 6h, 12h
        confidence_level=0.95,
        risk_free_rate=0.02  # 2% annual risk-free rate
    )
    
    print(f"Output directory: {config.output_dir}")
    print(f"Analysis periods: {config.lookforward_periods} (15-min intervals)")
    print(f"Minimum breakout threshold: {config.min_breakout_threshold}%")
    print()
    
    try:
        # Run the comprehensive analysis
        print("Starting analysis...")
        analyzer = BollingerBreakoutAnalyzer(config)
        results = analyzer.run_comprehensive_analysis()
        
        # Display results summary
        print("\n" + "="*80)
        print("ANALYSIS COMPLETED SUCCESSFULLY!")
        print("="*80)
        
        print(f"\nSummary Report: {results['summary_report']}")
        print(f"\nGenerated Files:")
        for file_type, filepath in results['generated_files'].items():
            print(f"  • {file_type}: {filepath}")
        
        print(f"\n📊 View the interactive chart: {results['generated_files']['interactive_bb_chart']}")
        print(f"📈 Check visualizations in: {config.output_dir}/")
        print(f"📋 Read full report: {results['summary_report']}")
        
        print("\n" + "="*80)
        print("ANALYSIS COMPLETE - Ready for trading insights!")
        print("="*80)
        
    except Exception as e:
        print(f"\nERROR during analysis: {e}")
        print("\nPlease check:")
        print("1. All required packages are installed (pip install -r src/analysis/requirements.txt)")
        print("2. The data file exists and is properly formatted")
        print("3. You have write permissions for the output directory")
        sys.exit(1)


if __name__ == "__main__":
    main()
