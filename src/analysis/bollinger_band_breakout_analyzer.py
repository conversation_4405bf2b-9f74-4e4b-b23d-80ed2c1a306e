#!/usr/bin/env python3
"""
Comprehensive Bollinger Band Breakout Analysis Script
=====================================================

This script provides comprehensive statistical analysis of Bollinger Band breakout events
for BTCUSDT 15-minute timeframe data, focusing on BB_Above_Upper_Pct and BB_Below_Lower_Pct
breakout patterns with advanced statistical, temporal, and risk-return analysis.

Author: Augment Agent
Date: 2025-08-10
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from pathlib import Path
import logging
from scipy import stats
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import plotly.offline as pyo

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Set plotting style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")


@dataclass
class BreakoutEvent:
    """Data class to represent a breakout event"""
    timestamp: pd.Timestamp
    breakout_type: str  # 'upper' or 'lower'
    breakout_magnitude: float
    price_at_breakout: float
    volume_at_breakout: float
    bb_upper: float
    bb_middle: float
    bb_lower: float
    

@dataclass
class AnalysisConfig:
    """Configuration for analysis parameters"""
    # File paths
    data_file: str = "output/BTCUSDT_15m_indicators_20250810_170018.csv"
    output_dir: str = "analysis_output"
    
    # Analysis parameters
    min_breakout_threshold: float = 0.01  # Minimum 0.01% breakout to consider
    lookforward_periods: List[int] = None  # Periods to analyze post-breakout performance
    confidence_level: float = 0.95  # For statistical significance testing
    
    # Risk metrics
    risk_free_rate: float = 0.02  # Annual risk-free rate for Sharpe ratio
    
    def __post_init__(self):
        if self.lookforward_periods is None:
            self.lookforward_periods = [1, 4, 12, 24, 48]  # 15min, 1h, 3h, 6h, 12h


class DataLoader:
    """Handles efficient loading and preprocessing of large CSV files"""
    
    def __init__(self, config: AnalysisConfig):
        self.config = config
        self.df = None
        
    def load_data(self) -> pd.DataFrame:
        """Load and preprocess the CSV data with memory optimization"""
        logger.info(f"Loading data from {self.config.data_file}")
        
        try:
            # Read CSV with optimized dtypes
            dtype_dict = {
                'open': 'float32',
                'high': 'float32', 
                'low': 'float32',
                'close': 'float32',
                'volume': 'float32',
                'BB_Upper': 'float32',
                'BB_Middle': 'float32',
                'BB_Lower': 'float32',
                'BB_Above_Upper_Pct': 'float32',
                'BB_Below_Lower_Pct': 'float32'
            }
            
            self.df = pd.read_csv(
                self.config.data_file,
                parse_dates=['timestamp'],
                index_col='timestamp',
                dtype=dtype_dict
            )
            
            logger.info(f"Loaded {len(self.df)} rows of data")
            logger.info(f"Data range: {self.df.index.min()} to {self.df.index.max()}")
            
            # Validate data
            self._validate_data()
            
            # Add derived columns
            self._add_derived_columns()
            
            return self.df
            
        except Exception as e:
            logger.error(f"Error loading data: {e}")
            raise
    
    def _validate_data(self):
        """Validate the loaded data"""
        required_columns = [
            'open', 'high', 'low', 'close', 'volume',
            'BB_Upper', 'BB_Middle', 'BB_Lower',
            'BB_Above_Upper_Pct', 'BB_Below_Lower_Pct'
        ]
        
        missing_columns = [col for col in required_columns if col not in self.df.columns]
        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")
        
        # Check for data quality issues
        logger.info("Data validation summary:")
        logger.info(f"  - Total rows: {len(self.df)}")
        logger.info(f"  - BB data available from row: {self.df['BB_Upper'].first_valid_index()}")
        logger.info(f"  - Upper breakouts: {self.df['BB_Above_Upper_Pct'].notna().sum()}")
        logger.info(f"  - Lower breakouts: {self.df['BB_Below_Lower_Pct'].notna().sum()}")
    
    def _add_derived_columns(self):
        """Add derived columns for analysis"""
        # Price returns
        self.df['returns'] = self.df['close'].pct_change()
        self.df['log_returns'] = np.log(self.df['close'] / self.df['close'].shift(1))
        
        # Volatility (rolling 24-period standard deviation of returns)
        self.df['volatility'] = self.df['returns'].rolling(window=24).std()
        
        # Volume moving average and relative volume
        self.df['volume_ma'] = self.df['volume'].rolling(window=24).mean()
        self.df['relative_volume'] = self.df['volume'] / self.df['volume_ma']
        
        # Bollinger Band position (where price is relative to bands)
        self.df['bb_position'] = (self.df['close'] - self.df['BB_Lower']) / (self.df['BB_Upper'] - self.df['BB_Lower'])
        
        # Bollinger Band width (normalized)
        self.df['bb_width'] = (self.df['BB_Upper'] - self.df['BB_Lower']) / self.df['BB_Middle']
        
        # Time-based features
        self.df['hour'] = self.df.index.hour
        self.df['day_of_week'] = self.df.index.dayofweek
        self.df['month'] = self.df.index.month
        
        logger.info("Added derived columns for enhanced analysis")


class StatisticalAnalyzer:
    """Handles statistical probability calculations and significance testing"""
    
    def __init__(self, df: pd.DataFrame, config: AnalysisConfig):
        self.df = df
        self.config = config
        
    def calculate_breakout_probabilities(self) -> Dict:
        """Calculate comprehensive breakout probability statistics"""
        logger.info("Calculating breakout probability statistics")
        
        # Filter valid Bollinger Band data
        valid_bb_data = self.df.dropna(subset=['BB_Upper', 'BB_Middle', 'BB_Lower'])
        
        # Total periods with valid BB data
        total_periods = len(valid_bb_data)
        
        # Upper breakouts
        upper_breakouts = valid_bb_data['BB_Above_Upper_Pct'].notna()
        upper_breakout_count = upper_breakouts.sum()
        upper_breakout_probability = upper_breakout_count / total_periods
        
        # Lower breakouts  
        lower_breakouts = valid_bb_data['BB_Below_Lower_Pct'].notna()
        lower_breakout_count = lower_breakouts.sum()
        lower_breakout_probability = lower_breakout_count / total_periods
        
        # Breakout magnitudes
        upper_magnitudes = valid_bb_data['BB_Above_Upper_Pct'].dropna()
        lower_magnitudes = valid_bb_data['BB_Below_Lower_Pct'].dropna()
        
        results = {
            'total_periods': total_periods,
            'upper_breakouts': {
                'count': upper_breakout_count,
                'probability': upper_breakout_probability,
                'frequency_per_day': upper_breakout_count / (total_periods / 96),  # 96 periods per day
                'magnitude_stats': self._calculate_magnitude_stats(upper_magnitudes)
            },
            'lower_breakouts': {
                'count': lower_breakout_count,
                'probability': lower_breakout_probability,
                'frequency_per_day': lower_breakout_count / (total_periods / 96),
                'magnitude_stats': self._calculate_magnitude_stats(lower_magnitudes)
            }
        }
        
        # Statistical significance testing
        results['statistical_tests'] = self._perform_statistical_tests(
            upper_magnitudes, lower_magnitudes
        )
        
        return results
    
    def _calculate_magnitude_stats(self, magnitudes: pd.Series) -> Dict:
        """Calculate comprehensive statistics for breakout magnitudes"""
        if len(magnitudes) == 0:
            return {'count': 0}
            
        return {
            'count': len(magnitudes),
            'mean': magnitudes.mean(),
            'median': magnitudes.median(),
            'std': magnitudes.std(),
            'min': magnitudes.min(),
            'max': magnitudes.max(),
            'percentiles': {
                '25th': magnitudes.quantile(0.25),
                '75th': magnitudes.quantile(0.75),
                '90th': magnitudes.quantile(0.90),
                '95th': magnitudes.quantile(0.95),
                '99th': magnitudes.quantile(0.99)
            }
        }
    
    def _perform_statistical_tests(self, upper_mags: pd.Series, lower_mags: pd.Series) -> Dict:
        """Perform statistical significance tests"""
        tests = {}
        
        # Test if upper and lower breakout magnitudes come from same distribution
        if len(upper_mags) > 0 and len(lower_mags) > 0:
            # Mann-Whitney U test (non-parametric)
            statistic, p_value = stats.mannwhitneyu(upper_mags, lower_mags, alternative='two-sided')
            tests['magnitude_difference'] = {
                'test': 'Mann-Whitney U',
                'statistic': statistic,
                'p_value': p_value,
                'significant': p_value < (1 - self.config.confidence_level)
            }
            
            # Kolmogorov-Smirnov test for distribution similarity
            ks_stat, ks_p = stats.ks_2samp(upper_mags, lower_mags)
            tests['distribution_similarity'] = {
                'test': 'Kolmogorov-Smirnov',
                'statistic': ks_stat,
                'p_value': ks_p,
                'significant': ks_p < (1 - self.config.confidence_level)
            }
        
        return tests


class TemporalAnalyzer:
    """Analyzes breakout patterns across different time periods"""

    def __init__(self, df: pd.DataFrame, config: AnalysisConfig):
        self.df = df
        self.config = config

    def analyze_temporal_patterns(self) -> Dict:
        """Analyze breakout patterns across time dimensions"""
        logger.info("Analyzing temporal breakout patterns")

        # Get breakout data
        upper_breakouts = self.df[self.df['BB_Above_Upper_Pct'].notna()].copy()
        lower_breakouts = self.df[self.df['BB_Below_Lower_Pct'].notna()].copy()

        results = {
            'hourly_patterns': self._analyze_hourly_patterns(upper_breakouts, lower_breakouts),
            'daily_patterns': self._analyze_daily_patterns(upper_breakouts, lower_breakouts),
            'weekly_patterns': self._analyze_weekly_patterns(upper_breakouts, lower_breakouts),
            'monthly_patterns': self._analyze_monthly_patterns(upper_breakouts, lower_breakouts),
            'seasonal_analysis': self._analyze_seasonal_patterns(upper_breakouts, lower_breakouts)
        }

        return results

    def _analyze_hourly_patterns(self, upper_breakouts: pd.DataFrame, lower_breakouts: pd.DataFrame) -> Dict:
        """Analyze breakout patterns by hour of day"""
        hourly_stats = {}

        for hour in range(24):
            upper_hour = upper_breakouts[upper_breakouts['hour'] == hour]
            lower_hour = lower_breakouts[lower_breakouts['hour'] == hour]

            hourly_stats[hour] = {
                'upper_count': len(upper_hour),
                'lower_count': len(lower_hour),
                'upper_avg_magnitude': upper_hour['BB_Above_Upper_Pct'].mean() if len(upper_hour) > 0 else 0,
                'lower_avg_magnitude': lower_hour['BB_Below_Lower_Pct'].mean() if len(lower_hour) > 0 else 0,
                'total_breakouts': len(upper_hour) + len(lower_hour)
            }

        # Find peak hours
        peak_upper_hour = max(hourly_stats.keys(), key=lambda h: hourly_stats[h]['upper_count'])
        peak_lower_hour = max(hourly_stats.keys(), key=lambda h: hourly_stats[h]['lower_count'])
        peak_total_hour = max(hourly_stats.keys(), key=lambda h: hourly_stats[h]['total_breakouts'])

        return {
            'hourly_distribution': hourly_stats,
            'peak_hours': {
                'upper_breakouts': peak_upper_hour,
                'lower_breakouts': peak_lower_hour,
                'total_breakouts': peak_total_hour
            }
        }

    def _analyze_daily_patterns(self, upper_breakouts: pd.DataFrame, lower_breakouts: pd.DataFrame) -> Dict:
        """Analyze breakout patterns by day of week"""
        daily_stats = {}
        day_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']

        for day in range(7):
            upper_day = upper_breakouts[upper_breakouts['day_of_week'] == day]
            lower_day = lower_breakouts[lower_breakouts['day_of_week'] == day]

            daily_stats[day_names[day]] = {
                'upper_count': len(upper_day),
                'lower_count': len(lower_day),
                'upper_avg_magnitude': upper_day['BB_Above_Upper_Pct'].mean() if len(upper_day) > 0 else 0,
                'lower_avg_magnitude': lower_day['BB_Below_Lower_Pct'].mean() if len(lower_day) > 0 else 0,
                'total_breakouts': len(upper_day) + len(lower_day)
            }

        return daily_stats

    def _analyze_weekly_patterns(self, upper_breakouts: pd.DataFrame, lower_breakouts: pd.DataFrame) -> Dict:
        """Analyze breakout patterns by week"""
        # Group by week
        upper_weekly = upper_breakouts.groupby(upper_breakouts.index.to_period('W')).agg({
            'BB_Above_Upper_Pct': ['count', 'mean', 'std']
        }).round(4)

        lower_weekly = lower_breakouts.groupby(lower_breakouts.index.to_period('W')).agg({
            'BB_Below_Lower_Pct': ['count', 'mean', 'std']
        }).round(4)

        return {
            'upper_weekly_stats': {
                'avg_breakouts_per_week': upper_weekly[('BB_Above_Upper_Pct', 'count')].mean(),
                'max_breakouts_per_week': upper_weekly[('BB_Above_Upper_Pct', 'count')].max(),
                'weeks_with_breakouts': (upper_weekly[('BB_Above_Upper_Pct', 'count')] > 0).sum()
            },
            'lower_weekly_stats': {
                'avg_breakouts_per_week': lower_weekly[('BB_Below_Lower_Pct', 'count')].mean(),
                'max_breakouts_per_week': lower_weekly[('BB_Below_Lower_Pct', 'count')].max(),
                'weeks_with_breakouts': (lower_weekly[('BB_Below_Lower_Pct', 'count')] > 0).sum()
            }
        }

    def _analyze_monthly_patterns(self, upper_breakouts: pd.DataFrame, lower_breakouts: pd.DataFrame) -> Dict:
        """Analyze breakout patterns by month"""
        monthly_stats = {}
        month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

        for month in range(1, 13):
            upper_month = upper_breakouts[upper_breakouts['month'] == month]
            lower_month = lower_breakouts[lower_breakouts['month'] == month]

            monthly_stats[month_names[month-1]] = {
                'upper_count': len(upper_month),
                'lower_count': len(lower_month),
                'upper_avg_magnitude': upper_month['BB_Above_Upper_Pct'].mean() if len(upper_month) > 0 else 0,
                'lower_avg_magnitude': lower_month['BB_Below_Lower_Pct'].mean() if len(lower_month) > 0 else 0,
                'total_breakouts': len(upper_month) + len(lower_month)
            }

        return monthly_stats

    def _analyze_seasonal_patterns(self, upper_breakouts: pd.DataFrame, lower_breakouts: pd.DataFrame) -> Dict:
        """Analyze seasonal patterns in breakouts"""
        # Define seasons
        seasons = {
            'Winter': [12, 1, 2],
            'Spring': [3, 4, 5],
            'Summer': [6, 7, 8],
            'Fall': [9, 10, 11]
        }

        seasonal_stats = {}
        for season, months in seasons.items():
            upper_season = upper_breakouts[upper_breakouts['month'].isin(months)]
            lower_season = lower_breakouts[lower_breakouts['month'].isin(months)]

            seasonal_stats[season] = {
                'upper_count': len(upper_season),
                'lower_count': len(lower_season),
                'upper_avg_magnitude': upper_season['BB_Above_Upper_Pct'].mean() if len(upper_season) > 0 else 0,
                'lower_avg_magnitude': lower_season['BB_Below_Lower_Pct'].mean() if len(lower_season) > 0 else 0,
                'total_breakouts': len(upper_season) + len(lower_season)
            }

        return seasonal_stats


class MarketConditionAnalyzer:
    """Analyzes breakouts in context of market conditions"""

    def __init__(self, df: pd.DataFrame, config: AnalysisConfig):
        self.df = df
        self.config = config

    def analyze_market_conditions(self) -> Dict:
        """Analyze breakouts in context of market volatility, volume, and trends"""
        logger.info("Analyzing market condition correlations")

        # Get breakout events
        upper_breakouts = self.df[self.df['BB_Above_Upper_Pct'].notna()].copy()
        lower_breakouts = self.df[self.df['BB_Below_Lower_Pct'].notna()].copy()

        results = {
            'volatility_analysis': self._analyze_volatility_context(upper_breakouts, lower_breakouts),
            'volume_analysis': self._analyze_volume_context(upper_breakouts, lower_breakouts),
            'trend_analysis': self._analyze_trend_context(upper_breakouts, lower_breakouts),
            'bb_width_analysis': self._analyze_bb_width_context(upper_breakouts, lower_breakouts)
        }

        return results

    def _analyze_volatility_context(self, upper_breakouts: pd.DataFrame, lower_breakouts: pd.DataFrame) -> Dict:
        """Analyze breakouts in different volatility regimes"""
        # Define volatility quantiles
        vol_quantiles = self.df['volatility'].quantile([0.25, 0.5, 0.75]).to_dict()

        def categorize_volatility(vol):
            if pd.isna(vol):
                return 'Unknown'
            elif vol <= vol_quantiles[0.25]:
                return 'Low'
            elif vol <= vol_quantiles[0.5]:
                return 'Medium-Low'
            elif vol <= vol_quantiles[0.75]:
                return 'Medium-High'
            else:
                return 'High'

        upper_breakouts['vol_regime'] = upper_breakouts['volatility'].apply(categorize_volatility)
        lower_breakouts['vol_regime'] = lower_breakouts['volatility'].apply(categorize_volatility)

        vol_analysis = {}
        for regime in ['Low', 'Medium-Low', 'Medium-High', 'High']:
            upper_regime = upper_breakouts[upper_breakouts['vol_regime'] == regime]
            lower_regime = lower_breakouts[lower_breakouts['vol_regime'] == regime]

            vol_analysis[regime] = {
                'upper_count': len(upper_regime),
                'lower_count': len(lower_regime),
                'upper_avg_magnitude': upper_regime['BB_Above_Upper_Pct'].mean() if len(upper_regime) > 0 else 0,
                'lower_avg_magnitude': lower_regime['BB_Below_Lower_Pct'].mean() if len(lower_regime) > 0 else 0,
                'avg_volatility': self.df[self.df['volatility'].apply(categorize_volatility) == regime]['volatility'].mean()
            }

        return {
            'volatility_regimes': vol_analysis,
            'volatility_thresholds': vol_quantiles
        }

    def _analyze_volume_context(self, upper_breakouts: pd.DataFrame, lower_breakouts: pd.DataFrame) -> Dict:
        """Analyze breakouts in different volume contexts"""
        # Define volume quantiles based on relative volume
        vol_quantiles = self.df['relative_volume'].quantile([0.25, 0.5, 0.75]).to_dict()

        def categorize_volume(rel_vol):
            if pd.isna(rel_vol):
                return 'Unknown'
            elif rel_vol <= vol_quantiles[0.25]:
                return 'Low'
            elif rel_vol <= vol_quantiles[0.5]:
                return 'Medium-Low'
            elif rel_vol <= vol_quantiles[0.75]:
                return 'Medium-High'
            else:
                return 'High'

        upper_breakouts['vol_regime'] = upper_breakouts['relative_volume'].apply(categorize_volume)
        lower_breakouts['vol_regime'] = lower_breakouts['relative_volume'].apply(categorize_volume)

        volume_analysis = {}
        for regime in ['Low', 'Medium-Low', 'Medium-High', 'High']:
            upper_regime = upper_breakouts[upper_breakouts['vol_regime'] == regime]
            lower_regime = lower_breakouts[lower_breakouts['vol_regime'] == regime]

            volume_analysis[regime] = {
                'upper_count': len(upper_regime),
                'lower_count': len(lower_regime),
                'upper_avg_magnitude': upper_regime['BB_Above_Upper_Pct'].mean() if len(upper_regime) > 0 else 0,
                'lower_avg_magnitude': lower_regime['BB_Below_Lower_Pct'].mean() if len(lower_regime) > 0 else 0,
                'avg_relative_volume': self.df[self.df['relative_volume'].apply(categorize_volume) == regime]['relative_volume'].mean()
            }

        return {
            'volume_regimes': volume_analysis,
            'volume_thresholds': vol_quantiles
        }

    def _analyze_trend_context(self, upper_breakouts: pd.DataFrame, lower_breakouts: pd.DataFrame) -> Dict:
        """Analyze breakouts in different trend contexts"""
        # Calculate trend using price position relative to BB middle
        def categorize_trend(bb_position):
            if pd.isna(bb_position):
                return 'Unknown'
            elif bb_position < 0.3:
                return 'Strong Downtrend'
            elif bb_position < 0.45:
                return 'Downtrend'
            elif bb_position < 0.55:
                return 'Sideways'
            elif bb_position < 0.7:
                return 'Uptrend'
            else:
                return 'Strong Uptrend'

        upper_breakouts['trend'] = upper_breakouts['bb_position'].apply(categorize_trend)
        lower_breakouts['trend'] = lower_breakouts['bb_position'].apply(categorize_trend)

        trend_analysis = {}
        trends = ['Strong Downtrend', 'Downtrend', 'Sideways', 'Uptrend', 'Strong Uptrend']

        for trend in trends:
            upper_trend = upper_breakouts[upper_breakouts['trend'] == trend]
            lower_trend = lower_breakouts[lower_breakouts['trend'] == trend]

            trend_analysis[trend] = {
                'upper_count': len(upper_trend),
                'lower_count': len(lower_trend),
                'upper_avg_magnitude': upper_trend['BB_Above_Upper_Pct'].mean() if len(upper_trend) > 0 else 0,
                'lower_avg_magnitude': lower_trend['BB_Below_Lower_Pct'].mean() if len(lower_trend) > 0 else 0
            }

        return trend_analysis

    def _analyze_bb_width_context(self, upper_breakouts: pd.DataFrame, lower_breakouts: pd.DataFrame) -> Dict:
        """Analyze breakouts in different Bollinger Band width contexts"""
        # Define BB width quantiles
        width_quantiles = self.df['bb_width'].quantile([0.25, 0.5, 0.75]).to_dict()

        def categorize_bb_width(width):
            if pd.isna(width):
                return 'Unknown'
            elif width <= width_quantiles[0.25]:
                return 'Narrow'
            elif width <= width_quantiles[0.5]:
                return 'Medium-Narrow'
            elif width <= width_quantiles[0.75]:
                return 'Medium-Wide'
            else:
                return 'Wide'

        upper_breakouts['width_regime'] = upper_breakouts['bb_width'].apply(categorize_bb_width)
        lower_breakouts['width_regime'] = lower_breakouts['bb_width'].apply(categorize_bb_width)

        width_analysis = {}
        for regime in ['Narrow', 'Medium-Narrow', 'Medium-Wide', 'Wide']:
            upper_regime = upper_breakouts[upper_breakouts['width_regime'] == regime]
            lower_regime = lower_breakouts[lower_breakouts['width_regime'] == regime]

            width_analysis[regime] = {
                'upper_count': len(upper_regime),
                'lower_count': len(lower_regime),
                'upper_avg_magnitude': upper_regime['BB_Above_Upper_Pct'].mean() if len(upper_regime) > 0 else 0,
                'lower_avg_magnitude': lower_regime['BB_Below_Lower_Pct'].mean() if len(lower_regime) > 0 else 0,
                'avg_bb_width': self.df[self.df['bb_width'].apply(categorize_bb_width) == regime]['bb_width'].mean()
            }

        return {
            'bb_width_regimes': width_analysis,
            'bb_width_thresholds': width_quantiles
        }


class BreakoutMagnitudeAnalyzer:
    """Analyzes the extent and duration of price movements following breakouts"""

    def __init__(self, df: pd.DataFrame, config: AnalysisConfig):
        self.df = df
        self.config = config

    def analyze_breakout_magnitude_and_duration(self) -> Dict:
        """Analyze breakout magnitude and subsequent price movements"""
        logger.info("Analyzing breakout magnitude and duration patterns")

        results = {
            'magnitude_distribution': self._analyze_magnitude_distribution(),
            'follow_through_analysis': self._analyze_follow_through(),
            'duration_analysis': self._analyze_breakout_duration(),
            'magnitude_vs_followthrough': self._analyze_magnitude_vs_followthrough()
        }

        return results

    def _analyze_magnitude_distribution(self) -> Dict:
        """Analyze the distribution of breakout magnitudes"""
        upper_mags = self.df['BB_Above_Upper_Pct'].dropna()
        lower_mags = self.df['BB_Below_Lower_Pct'].dropna()

        return {
            'upper_breakouts': {
                'distribution_stats': {
                    'mean': upper_mags.mean(),
                    'median': upper_mags.median(),
                    'std': upper_mags.std(),
                    'skewness': upper_mags.skew(),
                    'kurtosis': upper_mags.kurtosis()
                },
                'magnitude_ranges': {
                    'small (0-0.5%)': (upper_mags <= 0.5).sum(),
                    'medium (0.5-1.0%)': ((upper_mags > 0.5) & (upper_mags <= 1.0)).sum(),
                    'large (1.0-2.0%)': ((upper_mags > 1.0) & (upper_mags <= 2.0)).sum(),
                    'very_large (>2.0%)': (upper_mags > 2.0).sum()
                }
            },
            'lower_breakouts': {
                'distribution_stats': {
                    'mean': lower_mags.mean(),
                    'median': lower_mags.median(),
                    'std': lower_mags.std(),
                    'skewness': lower_mags.skew(),
                    'kurtosis': lower_mags.kurtosis()
                },
                'magnitude_ranges': {
                    'small (0-0.5%)': (lower_mags <= 0.5).sum(),
                    'medium (0.5-1.0%)': ((lower_mags > 0.5) & (lower_mags <= 1.0)).sum(),
                    'large (1.0-2.0%)': ((lower_mags > 1.0) & (lower_mags <= 2.0)).sum(),
                    'very_large (>2.0%)': (lower_mags > 2.0).sum()
                }
            }
        }

    def _analyze_follow_through(self) -> Dict:
        """Analyze price follow-through after breakouts"""
        upper_breakout_indices = self.df[self.df['BB_Above_Upper_Pct'].notna()].index
        lower_breakout_indices = self.df[self.df['BB_Below_Lower_Pct'].notna()].index

        follow_through_results = {}

        for period in self.config.lookforward_periods:
            period_results = {
                'upper_breakouts': self._calculate_follow_through(upper_breakout_indices, period, 'upper'),
                'lower_breakouts': self._calculate_follow_through(lower_breakout_indices, period, 'lower')
            }
            follow_through_results[f'{period}_periods'] = period_results

        return follow_through_results

    def _calculate_follow_through(self, breakout_indices: pd.Index, periods: int, breakout_type: str) -> Dict:
        """Calculate follow-through statistics for given periods"""
        follow_through_data = []

        for idx in breakout_indices:
            try:
                current_price = self.df.loc[idx, 'close']
                future_idx = self.df.index.get_loc(idx) + periods

                if future_idx < len(self.df):
                    future_price = self.df.iloc[future_idx]['close']

                    if breakout_type == 'upper':
                        # For upper breakouts, positive follow-through means price continued higher
                        follow_through = (future_price - current_price) / current_price * 100
                    else:
                        # For lower breakouts, positive follow-through means price continued lower
                        follow_through = (current_price - future_price) / current_price * 100

                    follow_through_data.append(follow_through)
            except (KeyError, IndexError):
                continue

        if not follow_through_data:
            return {'count': 0}

        follow_through_series = pd.Series(follow_through_data)

        return {
            'count': len(follow_through_data),
            'mean_follow_through': follow_through_series.mean(),
            'median_follow_through': follow_through_series.median(),
            'std_follow_through': follow_through_series.std(),
            'positive_follow_through_rate': (follow_through_series > 0).mean(),
            'strong_follow_through_rate': (follow_through_series > 1.0).mean(),  # >1% follow-through
            'percentiles': {
                '10th': follow_through_series.quantile(0.1),
                '25th': follow_through_series.quantile(0.25),
                '75th': follow_through_series.quantile(0.75),
                '90th': follow_through_series.quantile(0.9)
            }
        }

    def _analyze_breakout_duration(self) -> Dict:
        """Analyze how long breakouts persist"""
        # This analyzes how long price stays outside the bands after a breakout
        upper_breakout_indices = self.df[self.df['BB_Above_Upper_Pct'].notna()].index
        lower_breakout_indices = self.df[self.df['BB_Below_Lower_Pct'].notna()].index

        upper_durations = []
        lower_durations = []

        # Analyze upper breakout durations
        for idx in upper_breakout_indices:
            duration = self._calculate_breakout_duration(idx, 'upper')
            if duration is not None:
                upper_durations.append(duration)

        # Analyze lower breakout durations
        for idx in lower_breakout_indices:
            duration = self._calculate_breakout_duration(idx, 'lower')
            if duration is not None:
                lower_durations.append(duration)

        return {
            'upper_breakouts': self._duration_statistics(upper_durations),
            'lower_breakouts': self._duration_statistics(lower_durations)
        }

    def _calculate_breakout_duration(self, start_idx: pd.Timestamp, breakout_type: str) -> Optional[int]:
        """Calculate how many periods a breakout persists"""
        try:
            start_loc = self.df.index.get_loc(start_idx)
            duration = 0

            for i in range(start_loc + 1, len(self.df)):
                row = self.df.iloc[i]

                if breakout_type == 'upper':
                    # Check if high is still above upper band
                    if pd.notna(row['BB_Upper']) and row['high'] > row['BB_Upper']:
                        duration += 1
                    else:
                        break
                else:
                    # Check if low is still below lower band
                    if pd.notna(row['BB_Lower']) and row['low'] < row['BB_Lower']:
                        duration += 1
                    else:
                        break

                # Limit search to reasonable duration
                if duration > 100:  # More than 100 periods (25 hours)
                    break

            return duration
        except (KeyError, IndexError):
            return None

    def _duration_statistics(self, durations: List[int]) -> Dict:
        """Calculate statistics for breakout durations"""
        if not durations:
            return {'count': 0}

        duration_series = pd.Series(durations)

        return {
            'count': len(durations),
            'mean_duration': duration_series.mean(),
            'median_duration': duration_series.median(),
            'std_duration': duration_series.std(),
            'max_duration': duration_series.max(),
            'duration_distribution': {
                '1_period': (duration_series == 1).sum(),
                '2-3_periods': ((duration_series >= 2) & (duration_series <= 3)).sum(),
                '4-8_periods': ((duration_series >= 4) & (duration_series <= 8)).sum(),
                '9+_periods': (duration_series >= 9).sum()
            }
        }

    def _analyze_magnitude_vs_followthrough(self) -> Dict:
        """Analyze correlation between breakout magnitude and follow-through"""
        correlations = {}

        for period in self.config.lookforward_periods:
            upper_corr = self._calculate_magnitude_followthrough_correlation(period, 'upper')
            lower_corr = self._calculate_magnitude_followthrough_correlation(period, 'lower')

            correlations[f'{period}_periods'] = {
                'upper_correlation': upper_corr,
                'lower_correlation': lower_corr
            }

        return correlations

    def _calculate_magnitude_followthrough_correlation(self, periods: int, breakout_type: str) -> Dict:
        """Calculate correlation between magnitude and follow-through"""
        if breakout_type == 'upper':
            breakout_data = self.df[self.df['BB_Above_Upper_Pct'].notna()].copy()
            magnitude_col = 'BB_Above_Upper_Pct'
        else:
            breakout_data = self.df[self.df['BB_Below_Lower_Pct'].notna()].copy()
            magnitude_col = 'BB_Below_Lower_Pct'

        magnitudes = []
        follow_throughs = []

        for idx in breakout_data.index:
            try:
                magnitude = breakout_data.loc[idx, magnitude_col]
                current_price = breakout_data.loc[idx, 'close']

                future_idx = self.df.index.get_loc(idx) + periods
                if future_idx < len(self.df):
                    future_price = self.df.iloc[future_idx]['close']

                    if breakout_type == 'upper':
                        follow_through = (future_price - current_price) / current_price * 100
                    else:
                        follow_through = (current_price - future_price) / current_price * 100

                    magnitudes.append(magnitude)
                    follow_throughs.append(follow_through)
            except (KeyError, IndexError):
                continue

        if len(magnitudes) < 2:
            return {'correlation': None, 'p_value': None, 'sample_size': len(magnitudes)}

        correlation, p_value = stats.pearsonr(magnitudes, follow_throughs)

        return {
            'correlation': correlation,
            'p_value': p_value,
            'sample_size': len(magnitudes),
            'significant': p_value < 0.05 if not np.isnan(p_value) else False
        }


class RiskReturnAnalyzer:
    """Analyzes risk-return metrics for breakout-based strategies"""

    def __init__(self, df: pd.DataFrame, config: AnalysisConfig):
        self.df = df
        self.config = config

    def analyze_risk_return_metrics(self) -> Dict:
        """Calculate comprehensive risk-return metrics for breakout strategies"""
        logger.info("Calculating risk-return metrics for breakout strategies")

        results = {}

        for period in self.config.lookforward_periods:
            period_results = {
                'upper_breakout_strategy': self._calculate_strategy_metrics(period, 'upper'),
                'lower_breakout_strategy': self._calculate_strategy_metrics(period, 'lower'),
                'combined_strategy': self._calculate_combined_strategy_metrics(period)
            }
            results[f'{period}_period_strategy'] = period_results

        return results

    def _calculate_strategy_metrics(self, holding_period: int, breakout_type: str) -> Dict:
        """Calculate risk-return metrics for a specific breakout strategy"""
        if breakout_type == 'upper':
            breakout_data = self.df[self.df['BB_Above_Upper_Pct'].notna()].copy()
        else:
            breakout_data = self.df[self.df['BB_Below_Lower_Pct'].notna()].copy()

        returns = []

        for idx in breakout_data.index:
            try:
                entry_price = breakout_data.loc[idx, 'close']
                future_idx = self.df.index.get_loc(idx) + holding_period

                if future_idx < len(self.df):
                    exit_price = self.df.iloc[future_idx]['close']

                    if breakout_type == 'upper':
                        # Long strategy for upper breakouts
                        trade_return = (exit_price - entry_price) / entry_price
                    else:
                        # Short strategy for lower breakouts
                        trade_return = (entry_price - exit_price) / entry_price

                    returns.append(trade_return)
            except (KeyError, IndexError):
                continue

        if not returns:
            return {'trade_count': 0}

        returns_series = pd.Series(returns)

        # Calculate metrics
        total_return = (1 + returns_series).prod() - 1
        annualized_return = (1 + total_return) ** (365.25 * 24 * 4 / len(returns)) - 1  # 15-min periods
        volatility = returns_series.std() * np.sqrt(365.25 * 24 * 4)  # Annualized volatility

        # Risk metrics
        sharpe_ratio = (annualized_return - self.config.risk_free_rate) / volatility if volatility > 0 else 0
        max_drawdown = self._calculate_max_drawdown(returns_series)
        win_rate = (returns_series > 0).mean()

        # Value at Risk (95% confidence)
        var_95 = returns_series.quantile(0.05)

        return {
            'trade_count': len(returns),
            'total_return': total_return,
            'annualized_return': annualized_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'average_return': returns_series.mean(),
            'median_return': returns_series.median(),
            'best_trade': returns_series.max(),
            'worst_trade': returns_series.min(),
            'var_95': var_95,
            'profit_factor': self._calculate_profit_factor(returns_series),
            'calmar_ratio': annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0
        }

    def _calculate_max_drawdown(self, returns: pd.Series) -> float:
        """Calculate maximum drawdown"""
        cumulative = (1 + returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        return drawdown.min()

    def _calculate_profit_factor(self, returns: pd.Series) -> float:
        """Calculate profit factor (gross profit / gross loss)"""
        profits = returns[returns > 0].sum()
        losses = abs(returns[returns < 0].sum())
        return profits / losses if losses > 0 else np.inf

    def _calculate_combined_strategy_metrics(self, holding_period: int) -> Dict:
        """Calculate metrics for a combined long/short strategy"""
        upper_breakouts = self.df[self.df['BB_Above_Upper_Pct'].notna()].copy()
        lower_breakouts = self.df[self.df['BB_Below_Lower_Pct'].notna()].copy()

        all_trades = []

        # Long trades on upper breakouts
        for idx in upper_breakouts.index:
            try:
                entry_price = upper_breakouts.loc[idx, 'close']
                future_idx = self.df.index.get_loc(idx) + holding_period

                if future_idx < len(self.df):
                    exit_price = self.df.iloc[future_idx]['close']
                    trade_return = (exit_price - entry_price) / entry_price
                    all_trades.append((idx, trade_return, 'long'))
            except (KeyError, IndexError):
                continue

        # Short trades on lower breakouts
        for idx in lower_breakouts.index:
            try:
                entry_price = lower_breakouts.loc[idx, 'close']
                future_idx = self.df.index.get_loc(idx) + holding_period

                if future_idx < len(self.df):
                    exit_price = self.df.iloc[future_idx]['close']
                    trade_return = (entry_price - exit_price) / entry_price
                    all_trades.append((idx, trade_return, 'short'))
            except (KeyError, IndexError):
                continue

        if not all_trades:
            return {'trade_count': 0}

        # Sort by timestamp and calculate metrics
        all_trades.sort(key=lambda x: x[0])
        returns_series = pd.Series([trade[1] for trade in all_trades])

        # Calculate the same metrics as individual strategies
        total_return = (1 + returns_series).prod() - 1
        annualized_return = (1 + total_return) ** (365.25 * 24 * 4 / len(returns_series)) - 1
        volatility = returns_series.std() * np.sqrt(365.25 * 24 * 4)

        sharpe_ratio = (annualized_return - self.config.risk_free_rate) / volatility if volatility > 0 else 0
        max_drawdown = self._calculate_max_drawdown(returns_series)
        win_rate = (returns_series > 0).mean()
        var_95 = returns_series.quantile(0.05)

        # Strategy composition
        long_trades = sum(1 for trade in all_trades if trade[2] == 'long')
        short_trades = sum(1 for trade in all_trades if trade[2] == 'short')

        return {
            'trade_count': len(all_trades),
            'long_trades': long_trades,
            'short_trades': short_trades,
            'total_return': total_return,
            'annualized_return': annualized_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'average_return': returns_series.mean(),
            'median_return': returns_series.median(),
            'best_trade': returns_series.max(),
            'worst_trade': returns_series.min(),
            'var_95': var_95,
            'profit_factor': self._calculate_profit_factor(returns_series),
            'calmar_ratio': annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0
        }


class VisualizationEngine:
    """Creates comprehensive visualizations for breakout analysis"""

    def __init__(self, df: pd.DataFrame, config: AnalysisConfig):
        self.df = df
        self.config = config
        self.output_dir = Path(config.output_dir)
        self.output_dir.mkdir(exist_ok=True)

    def create_all_visualizations(self, analysis_results: Dict) -> Dict[str, str]:
        """Create all visualizations and return file paths"""
        logger.info("Creating comprehensive visualizations")

        viz_files = {}

        # 1. Breakout probability and magnitude distributions
        viz_files['probability_charts'] = self._create_probability_charts(analysis_results['statistical_analysis'])

        # 2. Temporal pattern heatmaps
        viz_files['temporal_heatmaps'] = self._create_temporal_heatmaps(analysis_results['temporal_analysis'])

        # 3. Market condition analysis charts
        viz_files['market_condition_charts'] = self._create_market_condition_charts(analysis_results['market_condition_analysis'])

        # 4. Follow-through analysis charts
        viz_files['followthrough_charts'] = self._create_followthrough_charts(analysis_results['magnitude_analysis'])

        # 5. Risk-return scatter plots
        viz_files['risk_return_charts'] = self._create_risk_return_charts(analysis_results['risk_return_analysis'])

        # 6. Interactive Bollinger Band chart with breakouts
        viz_files['interactive_bb_chart'] = self._create_interactive_bb_chart()

        return viz_files

    def _create_probability_charts(self, statistical_results: Dict) -> str:
        """Create probability and magnitude distribution charts"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Bollinger Band Breakout Probability Analysis', fontsize=16, fontweight='bold')

        # Upper breakout magnitude distribution
        upper_mags = self.df['BB_Above_Upper_Pct'].dropna()
        if len(upper_mags) > 0:
            axes[0, 0].hist(upper_mags, bins=50, alpha=0.7, color='red', edgecolor='black')
            axes[0, 0].set_title(f'Upper Breakout Magnitude Distribution\n(n={len(upper_mags)})')
            axes[0, 0].set_xlabel('Breakout Magnitude (%)')
            axes[0, 0].set_ylabel('Frequency')
            axes[0, 0].axvline(upper_mags.mean(), color='darkred', linestyle='--',
                              label=f'Mean: {upper_mags.mean():.2f}%')
            axes[0, 0].legend()

        # Lower breakout magnitude distribution
        lower_mags = self.df['BB_Below_Lower_Pct'].dropna()
        if len(lower_mags) > 0:
            axes[0, 1].hist(lower_mags, bins=50, alpha=0.7, color='blue', edgecolor='black')
            axes[0, 1].set_title(f'Lower Breakout Magnitude Distribution\n(n={len(lower_mags)})')
            axes[0, 1].set_xlabel('Breakout Magnitude (%)')
            axes[0, 1].set_ylabel('Frequency')
            axes[0, 1].axvline(lower_mags.mean(), color='darkblue', linestyle='--',
                              label=f'Mean: {lower_mags.mean():.2f}%')
            axes[0, 1].legend()

        # Breakout frequency over time
        breakout_freq = self.df.groupby(self.df.index.date).agg({
            'BB_Above_Upper_Pct': lambda x: x.notna().sum(),
            'BB_Below_Lower_Pct': lambda x: x.notna().sum()
        })

        axes[1, 0].plot(breakout_freq.index, breakout_freq['BB_Above_Upper_Pct'],
                       color='red', alpha=0.7, label='Upper Breakouts')
        axes[1, 0].plot(breakout_freq.index, breakout_freq['BB_Below_Lower_Pct'],
                       color='blue', alpha=0.7, label='Lower Breakouts')
        axes[1, 0].set_title('Daily Breakout Frequency')
        axes[1, 0].set_xlabel('Date')
        axes[1, 0].set_ylabel('Number of Breakouts')
        axes[1, 0].legend()
        axes[1, 0].tick_params(axis='x', rotation=45)

        # Probability comparison
        upper_prob = statistical_results['upper_breakouts']['probability']
        lower_prob = statistical_results['lower_breakouts']['probability']

        categories = ['Upper Breakouts', 'Lower Breakouts']
        probabilities = [upper_prob * 100, lower_prob * 100]

        bars = axes[1, 1].bar(categories, probabilities, color=['red', 'blue'], alpha=0.7)
        axes[1, 1].set_title('Breakout Probability Comparison')
        axes[1, 1].set_ylabel('Probability (%)')

        # Add value labels on bars
        for bar, prob in zip(bars, probabilities):
            axes[1, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                           f'{prob:.2f}%', ha='center', va='bottom', fontweight='bold')

        plt.tight_layout()
        filename = self.output_dir / 'probability_analysis.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close()

        return str(filename)

    def _create_temporal_heatmaps(self, temporal_results: Dict) -> str:
        """Create temporal pattern heatmaps"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Temporal Breakout Patterns', fontsize=16, fontweight='bold')

        # Hourly patterns heatmap
        hourly_data = temporal_results['hourly_patterns']['hourly_distribution']
        hours = list(range(24))
        upper_counts = [hourly_data[h]['upper_count'] for h in hours]
        lower_counts = [hourly_data[h]['lower_count'] for h in hours]

        # Create hourly heatmap data
        hourly_matrix = np.array([upper_counts, lower_counts])

        im1 = axes[0, 0].imshow(hourly_matrix, cmap='YlOrRd', aspect='auto')
        axes[0, 0].set_title('Hourly Breakout Patterns')
        axes[0, 0].set_xlabel('Hour of Day')
        axes[0, 0].set_yticks([0, 1])
        axes[0, 0].set_yticklabels(['Upper', 'Lower'])
        axes[0, 0].set_xticks(range(0, 24, 4))
        axes[0, 0].set_xticklabels(range(0, 24, 4))
        plt.colorbar(im1, ax=axes[0, 0], label='Breakout Count')

        # Daily patterns
        daily_data = temporal_results['daily_patterns']
        days = list(daily_data.keys())
        daily_upper = [daily_data[day]['upper_count'] for day in days]
        daily_lower = [daily_data[day]['lower_count'] for day in days]

        x = np.arange(len(days))
        width = 0.35

        axes[0, 1].bar(x - width/2, daily_upper, width, label='Upper', color='red', alpha=0.7)
        axes[0, 1].bar(x + width/2, daily_lower, width, label='Lower', color='blue', alpha=0.7)
        axes[0, 1].set_title('Daily Breakout Patterns')
        axes[0, 1].set_xlabel('Day of Week')
        axes[0, 1].set_ylabel('Breakout Count')
        axes[0, 1].set_xticks(x)
        axes[0, 1].set_xticklabels([day[:3] for day in days])
        axes[0, 1].legend()

        # Monthly patterns
        monthly_data = temporal_results['monthly_patterns']
        months = list(monthly_data.keys())
        monthly_upper = [monthly_data[month]['upper_count'] for month in months]
        monthly_lower = [monthly_data[month]['lower_count'] for month in months]

        x = np.arange(len(months))

        axes[1, 0].bar(x - width/2, monthly_upper, width, label='Upper', color='red', alpha=0.7)
        axes[1, 0].bar(x + width/2, monthly_lower, width, label='Lower', color='blue', alpha=0.7)
        axes[1, 0].set_title('Monthly Breakout Patterns')
        axes[1, 0].set_xlabel('Month')
        axes[1, 0].set_ylabel('Breakout Count')
        axes[1, 0].set_xticks(x)
        axes[1, 0].set_xticklabels(months, rotation=45)
        axes[1, 0].legend()

        # Seasonal patterns
        seasonal_data = temporal_results['seasonal_analysis']
        seasons = list(seasonal_data.keys())
        seasonal_upper = [seasonal_data[season]['upper_count'] for season in seasons]
        seasonal_lower = [seasonal_data[season]['lower_count'] for season in seasons]

        x = np.arange(len(seasons))

        axes[1, 1].bar(x - width/2, seasonal_upper, width, label='Upper', color='red', alpha=0.7)
        axes[1, 1].bar(x + width/2, seasonal_lower, width, label='Lower', color='blue', alpha=0.7)
        axes[1, 1].set_title('Seasonal Breakout Patterns')
        axes[1, 1].set_xlabel('Season')
        axes[1, 1].set_ylabel('Breakout Count')
        axes[1, 1].set_xticks(x)
        axes[1, 1].set_xticklabels(seasons)
        axes[1, 1].legend()

        plt.tight_layout()
        filename = self.output_dir / 'temporal_patterns.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close()

        return str(filename)

    def _create_market_condition_charts(self, market_results: Dict) -> str:
        """Create market condition analysis charts"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Market Condition Analysis', fontsize=16, fontweight='bold')

        # Volatility regime analysis
        vol_data = market_results['volatility_analysis']['volatility_regimes']
        regimes = list(vol_data.keys())
        vol_upper = [vol_data[regime]['upper_count'] for regime in regimes]
        vol_lower = [vol_data[regime]['lower_count'] for regime in regimes]

        x = np.arange(len(regimes))
        width = 0.35

        axes[0, 0].bar(x - width/2, vol_upper, width, label='Upper', color='red', alpha=0.7)
        axes[0, 0].bar(x + width/2, vol_lower, width, label='Lower', color='blue', alpha=0.7)
        axes[0, 0].set_title('Breakouts by Volatility Regime')
        axes[0, 0].set_xlabel('Volatility Regime')
        axes[0, 0].set_ylabel('Breakout Count')
        axes[0, 0].set_xticks(x)
        axes[0, 0].set_xticklabels(regimes, rotation=45)
        axes[0, 0].legend()

        # Volume regime analysis
        volume_data = market_results['volume_analysis']['volume_regimes']
        vol_regimes = list(volume_data.keys())
        volume_upper = [volume_data[regime]['upper_count'] for regime in vol_regimes]
        volume_lower = [volume_data[regime]['lower_count'] for regime in vol_regimes]

        x = np.arange(len(vol_regimes))

        axes[0, 1].bar(x - width/2, volume_upper, width, label='Upper', color='red', alpha=0.7)
        axes[0, 1].bar(x + width/2, volume_lower, width, label='Lower', color='blue', alpha=0.7)
        axes[0, 1].set_title('Breakouts by Volume Regime')
        axes[0, 1].set_xlabel('Volume Regime')
        axes[0, 1].set_ylabel('Breakout Count')
        axes[0, 1].set_xticks(x)
        axes[0, 1].set_xticklabels(vol_regimes, rotation=45)
        axes[0, 1].legend()

        # Trend analysis
        trend_data = market_results['trend_analysis']
        trends = list(trend_data.keys())
        trend_upper = [trend_data[trend]['upper_count'] for trend in trends]
        trend_lower = [trend_data[trend]['lower_count'] for trend in trends]

        x = np.arange(len(trends))

        axes[1, 0].bar(x - width/2, trend_upper, width, label='Upper', color='red', alpha=0.7)
        axes[1, 0].bar(x + width/2, trend_lower, width, label='Lower', color='blue', alpha=0.7)
        axes[1, 0].set_title('Breakouts by Trend Context')
        axes[1, 0].set_xlabel('Trend Context')
        axes[1, 0].set_ylabel('Breakout Count')
        axes[1, 0].set_xticks(x)
        axes[1, 0].set_xticklabels(trends, rotation=45)
        axes[1, 0].legend()

        # BB Width analysis
        width_data = market_results['bb_width_analysis']['bb_width_regimes']
        width_regimes = list(width_data.keys())
        width_upper = [width_data[regime]['upper_count'] for regime in width_regimes]
        width_lower = [width_data[regime]['lower_count'] for regime in width_regimes]

        x = np.arange(len(width_regimes))

        axes[1, 1].bar(x - width/2, width_upper, width, label='Upper', color='red', alpha=0.7)
        axes[1, 1].bar(x + width/2, width_lower, width, label='Lower', color='blue', alpha=0.7)
        axes[1, 1].set_title('Breakouts by BB Width')
        axes[1, 1].set_xlabel('BB Width Regime')
        axes[1, 1].set_ylabel('Breakout Count')
        axes[1, 1].set_xticks(x)
        axes[1, 1].set_xticklabels(width_regimes, rotation=45)
        axes[1, 1].legend()

        plt.tight_layout()
        filename = self.output_dir / 'market_conditions.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close()

        return str(filename)

    def _create_followthrough_charts(self, magnitude_results: Dict) -> str:
        """Create follow-through analysis charts"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Breakout Follow-Through Analysis', fontsize=16, fontweight='bold')

        # Follow-through success rates by period
        periods = self.config.lookforward_periods
        upper_success_rates = []
        lower_success_rates = []

        for period in periods:
            period_key = f'{period}_periods'
            if period_key in magnitude_results['follow_through_analysis']:
                upper_data = magnitude_results['follow_through_analysis'][period_key]['upper_breakouts']
                lower_data = magnitude_results['follow_through_analysis'][period_key]['lower_breakouts']

                upper_success_rates.append(upper_data.get('positive_follow_through_rate', 0) * 100)
                lower_success_rates.append(lower_data.get('positive_follow_through_rate', 0) * 100)
            else:
                upper_success_rates.append(0)
                lower_success_rates.append(0)

        x = np.arange(len(periods))
        width = 0.35

        axes[0, 0].bar(x - width/2, upper_success_rates, width, label='Upper', color='red', alpha=0.7)
        axes[0, 0].bar(x + width/2, lower_success_rates, width, label='Lower', color='blue', alpha=0.7)
        axes[0, 0].set_title('Follow-Through Success Rate by Holding Period')
        axes[0, 0].set_xlabel('Holding Period (15-min intervals)')
        axes[0, 0].set_ylabel('Success Rate (%)')
        axes[0, 0].set_xticks(x)
        axes[0, 0].set_xticklabels([f'{p}' for p in periods])
        axes[0, 0].legend()

        # Duration analysis
        duration_data = magnitude_results['duration_analysis']
        if 'upper_breakouts' in duration_data and duration_data['upper_breakouts']['count'] > 0:
            upper_durations = duration_data['upper_breakouts']['duration_distribution']
            duration_labels = list(upper_durations.keys())
            duration_values = list(upper_durations.values())

            axes[0, 1].pie(duration_values, labels=duration_labels, autopct='%1.1f%%', startangle=90)
            axes[0, 1].set_title('Upper Breakout Duration Distribution')

        if 'lower_breakouts' in duration_data and duration_data['lower_breakouts']['count'] > 0:
            lower_durations = duration_data['lower_breakouts']['duration_distribution']
            duration_labels = list(lower_durations.keys())
            duration_values = list(lower_durations.values())

            axes[1, 0].pie(duration_values, labels=duration_labels, autopct='%1.1f%%', startangle=90)
            axes[1, 0].set_title('Lower Breakout Duration Distribution')

        # Magnitude vs Follow-through correlation
        correlations = magnitude_results['magnitude_vs_followthrough']
        periods_corr = []
        upper_corrs = []
        lower_corrs = []

        for period in periods:
            period_key = f'{period}_periods'
            if period_key in correlations:
                periods_corr.append(period)
                upper_corr = correlations[period_key]['upper_correlation']['correlation']
                lower_corr = correlations[period_key]['lower_correlation']['correlation']
                upper_corrs.append(upper_corr if upper_corr is not None else 0)
                lower_corrs.append(lower_corr if lower_corr is not None else 0)

        if periods_corr:
            axes[1, 1].plot(periods_corr, upper_corrs, 'ro-', label='Upper Breakouts', linewidth=2)
            axes[1, 1].plot(periods_corr, lower_corrs, 'bo-', label='Lower Breakouts', linewidth=2)
            axes[1, 1].set_title('Magnitude vs Follow-Through Correlation')
            axes[1, 1].set_xlabel('Holding Period (15-min intervals)')
            axes[1, 1].set_ylabel('Correlation Coefficient')
            axes[1, 1].axhline(y=0, color='gray', linestyle='--', alpha=0.5)
            axes[1, 1].legend()
            axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()
        filename = self.output_dir / 'followthrough_analysis.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close()

        return str(filename)

    def _create_risk_return_charts(self, risk_return_results: Dict) -> str:
        """Create risk-return analysis charts"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Risk-Return Analysis', fontsize=16, fontweight='bold')

        # Extract data for different holding periods
        periods = self.config.lookforward_periods
        upper_returns = []
        upper_sharpe = []
        lower_returns = []
        lower_sharpe = []
        combined_returns = []
        combined_sharpe = []

        for period in periods:
            period_key = f'{period}_period_strategy'
            if period_key in risk_return_results:
                data = risk_return_results[period_key]

                upper_data = data['upper_breakout_strategy']
                if upper_data['trade_count'] > 0:
                    upper_returns.append(upper_data['annualized_return'] * 100)
                    upper_sharpe.append(upper_data['sharpe_ratio'])
                else:
                    upper_returns.append(0)
                    upper_sharpe.append(0)

                lower_data = data['lower_breakout_strategy']
                if lower_data['trade_count'] > 0:
                    lower_returns.append(lower_data['annualized_return'] * 100)
                    lower_sharpe.append(lower_data['sharpe_ratio'])
                else:
                    lower_returns.append(0)
                    lower_sharpe.append(0)

                combined_data = data['combined_strategy']
                if combined_data['trade_count'] > 0:
                    combined_returns.append(combined_data['annualized_return'] * 100)
                    combined_sharpe.append(combined_data['sharpe_ratio'])
                else:
                    combined_returns.append(0)
                    combined_sharpe.append(0)

        # Annualized returns by holding period
        x = np.arange(len(periods))
        width = 0.25

        axes[0, 0].bar(x - width, upper_returns, width, label='Upper Breakouts', color='red', alpha=0.7)
        axes[0, 0].bar(x, lower_returns, width, label='Lower Breakouts', color='blue', alpha=0.7)
        axes[0, 0].bar(x + width, combined_returns, width, label='Combined Strategy', color='green', alpha=0.7)
        axes[0, 0].set_title('Annualized Returns by Strategy')
        axes[0, 0].set_xlabel('Holding Period (15-min intervals)')
        axes[0, 0].set_ylabel('Annualized Return (%)')
        axes[0, 0].set_xticks(x)
        axes[0, 0].set_xticklabels([f'{p}' for p in periods])
        axes[0, 0].legend()
        axes[0, 0].axhline(y=0, color='gray', linestyle='--', alpha=0.5)

        # Sharpe ratios
        axes[0, 1].bar(x - width, upper_sharpe, width, label='Upper Breakouts', color='red', alpha=0.7)
        axes[0, 1].bar(x, lower_sharpe, width, label='Lower Breakouts', color='blue', alpha=0.7)
        axes[0, 1].bar(x + width, combined_sharpe, width, label='Combined Strategy', color='green', alpha=0.7)
        axes[0, 1].set_title('Sharpe Ratios by Strategy')
        axes[0, 1].set_xlabel('Holding Period (15-min intervals)')
        axes[0, 1].set_ylabel('Sharpe Ratio')
        axes[0, 1].set_xticks(x)
        axes[0, 1].set_xticklabels([f'{p}' for p in periods])
        axes[0, 1].legend()
        axes[0, 1].axhline(y=0, color='gray', linestyle='--', alpha=0.5)

        # Risk-Return scatter plot (for best performing period)
        best_period_idx = np.argmax(combined_sharpe) if combined_sharpe else 0
        best_period = periods[best_period_idx]
        best_period_key = f'{best_period}_period_strategy'

        if best_period_key in risk_return_results:
            data = risk_return_results[best_period_key]

            strategies = ['Upper Breakouts', 'Lower Breakouts', 'Combined Strategy']
            returns_scatter = [
                data['upper_breakout_strategy']['annualized_return'] * 100,
                data['lower_breakout_strategy']['annualized_return'] * 100,
                data['combined_strategy']['annualized_return'] * 100
            ]
            volatilities = [
                data['upper_breakout_strategy']['volatility'] * 100,
                data['lower_breakout_strategy']['volatility'] * 100,
                data['combined_strategy']['volatility'] * 100
            ]
            colors = ['red', 'blue', 'green']

            for i, (strategy, ret, vol, color) in enumerate(zip(strategies, returns_scatter, volatilities, colors)):
                axes[1, 0].scatter(vol, ret, s=100, c=color, alpha=0.7, label=strategy)

            axes[1, 0].set_title(f'Risk-Return Profile (Best Period: {best_period})')
            axes[1, 0].set_xlabel('Volatility (%)')
            axes[1, 0].set_ylabel('Annualized Return (%)')
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)

        # Win rates comparison
        win_rates_upper = []
        win_rates_lower = []
        win_rates_combined = []

        for period in periods:
            period_key = f'{period}_period_strategy'
            if period_key in risk_return_results:
                data = risk_return_results[period_key]
                win_rates_upper.append(data['upper_breakout_strategy'].get('win_rate', 0) * 100)
                win_rates_lower.append(data['lower_breakout_strategy'].get('win_rate', 0) * 100)
                win_rates_combined.append(data['combined_strategy'].get('win_rate', 0) * 100)

        axes[1, 1].bar(x - width, win_rates_upper, width, label='Upper Breakouts', color='red', alpha=0.7)
        axes[1, 1].bar(x, win_rates_lower, width, label='Lower Breakouts', color='blue', alpha=0.7)
        axes[1, 1].bar(x + width, win_rates_combined, width, label='Combined Strategy', color='green', alpha=0.7)
        axes[1, 1].set_title('Win Rates by Strategy')
        axes[1, 1].set_xlabel('Holding Period (15-min intervals)')
        axes[1, 1].set_ylabel('Win Rate (%)')
        axes[1, 1].set_xticks(x)
        axes[1, 1].set_xticklabels([f'{p}' for p in periods])
        axes[1, 1].legend()
        axes[1, 1].axhline(y=50, color='gray', linestyle='--', alpha=0.5, label='50% baseline')

        plt.tight_layout()
        filename = self.output_dir / 'risk_return_analysis.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close()

        return str(filename)

    def _create_interactive_bb_chart(self) -> str:
        """Create interactive Bollinger Band chart with breakouts highlighted"""
        # Sample recent data for interactive chart (last 1000 points for performance)
        recent_data = self.df.tail(1000).copy()

        fig = go.Figure()

        # Add candlestick chart
        fig.add_trace(go.Candlestick(
            x=recent_data.index,
            open=recent_data['open'],
            high=recent_data['high'],
            low=recent_data['low'],
            close=recent_data['close'],
            name='BTCUSDT',
            increasing_line_color='green',
            decreasing_line_color='red'
        ))

        # Add Bollinger Bands
        fig.add_trace(go.Scatter(
            x=recent_data.index,
            y=recent_data['BB_Upper'],
            mode='lines',
            name='BB Upper',
            line=dict(color='purple', width=1),
            opacity=0.7
        ))

        fig.add_trace(go.Scatter(
            x=recent_data.index,
            y=recent_data['BB_Middle'],
            mode='lines',
            name='BB Middle',
            line=dict(color='orange', width=1),
            opacity=0.7
        ))

        fig.add_trace(go.Scatter(
            x=recent_data.index,
            y=recent_data['BB_Lower'],
            mode='lines',
            name='BB Lower',
            line=dict(color='purple', width=1),
            opacity=0.7,
            fill='tonexty',
            fillcolor='rgba(128, 0, 128, 0.1)'
        ))

        # Highlight breakouts
        upper_breakouts = recent_data[recent_data['BB_Above_Upper_Pct'].notna()]
        lower_breakouts = recent_data[recent_data['BB_Below_Lower_Pct'].notna()]

        if len(upper_breakouts) > 0:
            fig.add_trace(go.Scatter(
                x=upper_breakouts.index,
                y=upper_breakouts['high'],
                mode='markers',
                name='Upper Breakouts',
                marker=dict(color='red', size=8, symbol='triangle-up'),
                text=[f'Magnitude: {mag:.2f}%' for mag in upper_breakouts['BB_Above_Upper_Pct']],
                hovertemplate='<b>Upper Breakout</b><br>Time: %{x}<br>Price: %{y}<br>%{text}<extra></extra>'
            ))

        if len(lower_breakouts) > 0:
            fig.add_trace(go.Scatter(
                x=lower_breakouts.index,
                y=lower_breakouts['low'],
                mode='markers',
                name='Lower Breakouts',
                marker=dict(color='blue', size=8, symbol='triangle-down'),
                text=[f'Magnitude: {mag:.2f}%' for mag in lower_breakouts['BB_Below_Lower_Pct']],
                hovertemplate='<b>Lower Breakout</b><br>Time: %{x}<br>Price: %{y}<br>%{text}<extra></extra>'
            ))

        fig.update_layout(
            title='BTCUSDT 15m with Bollinger Band Breakouts',
            xaxis_title='Time',
            yaxis_title='Price (USDT)',
            template='plotly_white',
            height=600,
            showlegend=True
        )

        filename = self.output_dir / 'interactive_bb_chart.html'
        fig.write_html(str(filename))

        return str(filename)


class ReportGenerator:
    """Generates comprehensive analysis reports"""

    def __init__(self, config: AnalysisConfig):
        self.config = config
        self.output_dir = Path(config.output_dir)

    def generate_summary_report(self, analysis_results: Dict, viz_files: Dict) -> str:
        """Generate a comprehensive summary report"""
        logger.info("Generating comprehensive summary report")

        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("BOLLINGER BAND BREAKOUT ANALYSIS REPORT")
        report_lines.append("=" * 80)
        report_lines.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"Data File: {self.config.data_file}")
        report_lines.append("")

        # Executive Summary
        report_lines.append("EXECUTIVE SUMMARY")
        report_lines.append("-" * 40)

        stats = analysis_results['statistical_analysis']
        report_lines.append(f"Total Analysis Period: {stats['total_periods']:,} periods")
        report_lines.append(f"Upper Breakout Probability: {stats['upper_breakouts']['probability']:.4f} ({stats['upper_breakouts']['probability']*100:.2f}%)")
        report_lines.append(f"Lower Breakout Probability: {stats['lower_breakouts']['probability']:.4f} ({stats['lower_breakouts']['probability']*100:.2f}%)")
        report_lines.append(f"Upper Breakouts per Day: {stats['upper_breakouts']['frequency_per_day']:.2f}")
        report_lines.append(f"Lower Breakouts per Day: {stats['lower_breakouts']['frequency_per_day']:.2f}")
        report_lines.append("")

        # Statistical Analysis
        report_lines.append("STATISTICAL ANALYSIS")
        report_lines.append("-" * 40)

        upper_stats = stats['upper_breakouts']['magnitude_stats']
        lower_stats = stats['lower_breakouts']['magnitude_stats']

        report_lines.append("Upper Breakout Magnitudes:")
        report_lines.append(f"  Mean: {upper_stats['mean']:.3f}%")
        report_lines.append(f"  Median: {upper_stats['median']:.3f}%")
        report_lines.append(f"  Standard Deviation: {upper_stats['std']:.3f}%")
        report_lines.append(f"  95th Percentile: {upper_stats['percentiles']['95th']:.3f}%")
        report_lines.append("")

        report_lines.append("Lower Breakout Magnitudes:")
        report_lines.append(f"  Mean: {lower_stats['mean']:.3f}%")
        report_lines.append(f"  Median: {lower_stats['median']:.3f}%")
        report_lines.append(f"  Standard Deviation: {lower_stats['std']:.3f}%")
        report_lines.append(f"  95th Percentile: {lower_stats['percentiles']['95th']:.3f}%")
        report_lines.append("")

        # Temporal Patterns
        report_lines.append("TEMPORAL PATTERNS")
        report_lines.append("-" * 40)

        temporal = analysis_results['temporal_analysis']
        peak_hours = temporal['hourly_patterns']['peak_hours']

        report_lines.append(f"Peak Upper Breakout Hour: {peak_hours['upper_breakouts']}:00")
        report_lines.append(f"Peak Lower Breakout Hour: {peak_hours['lower_breakouts']}:00")
        report_lines.append(f"Peak Total Breakout Hour: {peak_hours['total_breakouts']}:00")
        report_lines.append("")

        # Market Conditions
        report_lines.append("MARKET CONDITION INSIGHTS")
        report_lines.append("-" * 40)

        market_conditions = analysis_results['market_condition_analysis']
        vol_regimes = market_conditions['volatility_analysis']['volatility_regimes']

        # Find regime with most breakouts
        max_breakouts_regime = max(vol_regimes.keys(),
                                 key=lambda x: vol_regimes[x]['upper_count'] + vol_regimes[x]['lower_count'])

        report_lines.append(f"Most Active Volatility Regime: {max_breakouts_regime}")
        report_lines.append(f"  Upper Breakouts: {vol_regimes[max_breakouts_regime]['upper_count']}")
        report_lines.append(f"  Lower Breakouts: {vol_regimes[max_breakouts_regime]['lower_count']}")
        report_lines.append("")

        # Risk-Return Analysis
        report_lines.append("RISK-RETURN ANALYSIS")
        report_lines.append("-" * 40)

        # Find best performing strategy
        risk_return = analysis_results['risk_return_analysis']
        best_strategy_info = self._find_best_strategy(risk_return)

        if best_strategy_info:
            strategy_name, period, metrics = best_strategy_info
            report_lines.append(f"Best Performing Strategy: {strategy_name} ({period} periods)")
            report_lines.append(f"  Annualized Return: {metrics['annualized_return']*100:.2f}%")
            report_lines.append(f"  Sharpe Ratio: {metrics['sharpe_ratio']:.3f}")
            report_lines.append(f"  Win Rate: {metrics['win_rate']*100:.1f}%")
            report_lines.append(f"  Max Drawdown: {metrics['max_drawdown']*100:.2f}%")
            report_lines.append(f"  Total Trades: {metrics['trade_count']}")
        report_lines.append("")

        # Key Insights and Recommendations
        report_lines.append("KEY INSIGHTS & TRADING IMPLICATIONS")
        report_lines.append("-" * 40)

        insights = self._generate_insights(analysis_results)
        for insight in insights:
            report_lines.append(f"• {insight}")
        report_lines.append("")

        # Generated Files
        report_lines.append("GENERATED FILES")
        report_lines.append("-" * 40)
        for file_type, filepath in viz_files.items():
            report_lines.append(f"{file_type}: {filepath}")
        report_lines.append("")

        report_lines.append("=" * 80)

        # Save report
        report_filename = self.output_dir / 'bollinger_breakout_analysis_report.txt'
        with open(report_filename, 'w') as f:
            f.write('\n'.join(report_lines))

        return str(report_filename)

    def _find_best_strategy(self, risk_return_results: Dict) -> Optional[Tuple[str, str, Dict]]:
        """Find the best performing strategy based on Sharpe ratio"""
        best_sharpe = -999
        best_strategy = None

        for period_key, period_data in risk_return_results.items():
            for strategy_name, strategy_data in period_data.items():
                if strategy_data.get('trade_count', 0) > 0:
                    sharpe = strategy_data.get('sharpe_ratio', -999)
                    if sharpe > best_sharpe:
                        best_sharpe = sharpe
                        best_strategy = (strategy_name, period_key, strategy_data)

        return best_strategy

    def _generate_insights(self, analysis_results: Dict) -> List[str]:
        """Generate key insights from the analysis"""
        insights = []

        stats = analysis_results['statistical_analysis']
        temporal = analysis_results['temporal_analysis']
        market = analysis_results['market_condition_analysis']

        # Probability insights
        upper_prob = stats['upper_breakouts']['probability']
        lower_prob = stats['lower_breakouts']['probability']

        if upper_prob > lower_prob:
            insights.append(f"Upper breakouts are {upper_prob/lower_prob:.1f}x more likely than lower breakouts")
        else:
            insights.append(f"Lower breakouts are {lower_prob/upper_prob:.1f}x more likely than upper breakouts")

        # Magnitude insights
        upper_mag = stats['upper_breakouts']['magnitude_stats']['mean']
        lower_mag = stats['lower_breakouts']['magnitude_stats']['mean']

        if upper_mag > lower_mag:
            insights.append(f"Upper breakouts are typically {upper_mag/lower_mag:.1f}x stronger in magnitude")
        else:
            insights.append(f"Lower breakouts are typically {lower_mag/upper_mag:.1f}x stronger in magnitude")

        # Temporal insights
        peak_hour = temporal['hourly_patterns']['peak_hours']['total_breakouts']
        insights.append(f"Most breakouts occur at {peak_hour}:00 UTC+8 (consider timezone for trading)")

        # Market condition insights
        vol_regimes = market['volatility_analysis']['volatility_regimes']
        high_vol_breakouts = vol_regimes['High']['upper_count'] + vol_regimes['High']['lower_count']
        low_vol_breakouts = vol_regimes['Low']['upper_count'] + vol_regimes['Low']['lower_count']

        if high_vol_breakouts > low_vol_breakouts:
            insights.append("Breakouts are more frequent during high volatility periods")
        else:
            insights.append("Breakouts occur more often during low volatility periods")

        # Statistical significance
        if 'statistical_tests' in stats and stats['statistical_tests']:
            mag_test = stats['statistical_tests'].get('magnitude_difference', {})
            if mag_test.get('significant', False):
                insights.append("Upper and lower breakout magnitudes are statistically different")

        return insights

    def export_detailed_data(self, analysis_results: Dict, df: pd.DataFrame) -> str:
        """Export detailed analysis data to CSV"""
        detailed_data = []

        # Add breakout events with context
        upper_breakouts = df[df['BB_Above_Upper_Pct'].notna()].copy()
        lower_breakouts = df[df['BB_Below_Lower_Pct'].notna()].copy()

        for idx, row in upper_breakouts.iterrows():
            detailed_data.append({
                'timestamp': idx,
                'breakout_type': 'upper',
                'magnitude': row['BB_Above_Upper_Pct'],
                'price': row['close'],
                'volume': row['volume'],
                'volatility': row.get('volatility', np.nan),
                'relative_volume': row.get('relative_volume', np.nan),
                'bb_width': row.get('bb_width', np.nan),
                'hour': row.get('hour', np.nan),
                'day_of_week': row.get('day_of_week', np.nan)
            })

        for idx, row in lower_breakouts.iterrows():
            detailed_data.append({
                'timestamp': idx,
                'breakout_type': 'lower',
                'magnitude': row['BB_Below_Lower_Pct'],
                'price': row['close'],
                'volume': row['volume'],
                'volatility': row.get('volatility', np.nan),
                'relative_volume': row.get('relative_volume', np.nan),
                'bb_width': row.get('bb_width', np.nan),
                'hour': row.get('hour', np.nan),
                'day_of_week': row.get('day_of_week', np.nan)
            })

        detailed_df = pd.DataFrame(detailed_data)
        detailed_df = detailed_df.sort_values('timestamp')

        filename = self.output_dir / 'detailed_breakout_data.csv'
        detailed_df.to_csv(filename, index=False)

        return str(filename)


class BollingerBreakoutAnalyzer:
    """Main analyzer class that orchestrates the comprehensive analysis"""

    def __init__(self, config: AnalysisConfig = None):
        self.config = config or AnalysisConfig()
        self.df = None

    def run_comprehensive_analysis(self) -> Dict:
        """Run the complete Bollinger Band breakout analysis"""
        logger.info("Starting comprehensive Bollinger Band breakout analysis")

        # Create output directory
        Path(self.config.output_dir).mkdir(exist_ok=True)

        # 1. Load and preprocess data
        logger.info("Step 1: Loading and preprocessing data")
        data_loader = DataLoader(self.config)
        self.df = data_loader.load_data()

        # 2. Statistical analysis
        logger.info("Step 2: Statistical probability analysis")
        statistical_analyzer = StatisticalAnalyzer(self.df, self.config)
        statistical_results = statistical_analyzer.calculate_breakout_probabilities()

        # 3. Temporal analysis
        logger.info("Step 3: Temporal pattern analysis")
        temporal_analyzer = TemporalAnalyzer(self.df, self.config)
        temporal_results = temporal_analyzer.analyze_temporal_patterns()

        # 4. Market condition analysis
        logger.info("Step 4: Market condition analysis")
        market_analyzer = MarketConditionAnalyzer(self.df, self.config)
        market_results = market_analyzer.analyze_market_conditions()

        # 5. Breakout magnitude and duration analysis
        logger.info("Step 5: Breakout magnitude and duration analysis")
        magnitude_analyzer = BreakoutMagnitudeAnalyzer(self.df, self.config)
        magnitude_results = magnitude_analyzer.analyze_breakout_magnitude_and_duration()

        # 6. Risk-return analysis
        logger.info("Step 6: Risk-return metrics analysis")
        risk_analyzer = RiskReturnAnalyzer(self.df, self.config)
        risk_return_results = risk_analyzer.analyze_risk_return_metrics()

        # 7. Generate visualizations
        logger.info("Step 7: Creating visualizations")
        viz_engine = VisualizationEngine(self.df, self.config)

        analysis_results = {
            'statistical_analysis': statistical_results,
            'temporal_analysis': temporal_results,
            'market_condition_analysis': market_results,
            'magnitude_analysis': magnitude_results,
            'risk_return_analysis': risk_return_results
        }

        viz_files = viz_engine.create_all_visualizations(analysis_results)

        # 8. Generate reports
        logger.info("Step 8: Generating reports")
        report_generator = ReportGenerator(self.config)
        summary_report = report_generator.generate_summary_report(analysis_results, viz_files)
        detailed_data_file = report_generator.export_detailed_data(analysis_results, self.df)

        # Add report files to results
        viz_files['summary_report'] = summary_report
        viz_files['detailed_data'] = detailed_data_file

        logger.info("Analysis complete!")
        logger.info(f"Results saved to: {self.config.output_dir}")

        return {
            'analysis_results': analysis_results,
            'generated_files': viz_files,
            'summary_report': summary_report
        }


def main():
    """Main execution function"""
    # Configure analysis
    config = AnalysisConfig(
        data_file="output/BTCUSDT_15m_indicators_20250810_170018.csv",
        output_dir="analysis_output",
        min_breakout_threshold=0.01,
        lookforward_periods=[1, 4, 12, 24, 48],  # 15min, 1h, 3h, 6h, 12h
        confidence_level=0.95,
        risk_free_rate=0.02
    )

    # Run analysis
    analyzer = BollingerBreakoutAnalyzer(config)
    results = analyzer.run_comprehensive_analysis()

    # Print summary
    print("\n" + "="*80)
    print("BOLLINGER BAND BREAKOUT ANALYSIS COMPLETED")
    print("="*80)
    print(f"Summary report: {results['summary_report']}")
    print("\nGenerated files:")
    for file_type, filepath in results['generated_files'].items():
        print(f"  {file_type}: {filepath}")
    print("="*80)


if __name__ == "__main__":
    main()
