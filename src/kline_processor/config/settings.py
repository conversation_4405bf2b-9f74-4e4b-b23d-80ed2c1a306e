"""
Configuration management using Pydantic Settings.

This module provides application configuration with automatic environment variable loading,
type validation, and comprehensive settings for all system components.
"""

from typing import List, Any, Optional, Union
from pydantic import Field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application configuration using Pydantic settings with environment variable support."""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )
    
    # API Configuration
    kline_url_v2: str = Field(
        default="https://fapi.binance.com",
        description="Exchange API base URL for continuous K-lines"
    )
    base_url: str = Field(
        default="wss://fstream.binance.com",
        description="WebSocket base URL for real-time data streams"
    )
    
    # Redis Configuration  
    redis_host: str = Field(
        default="127.0.0.1",
        description="Redis server hostname"
    )
    redis_port: int = Field(
        default=6379,
        ge=1,
        le=65535,
        description="Redis server port"
    )
    redis_password: Optional[str] = Field(
        default="valkey_password",
        description="Redis server password"
    )
    redis_db: int = Field(
        default=3,
        ge=0,
        le=15,
        description="Redis database number"
    )
    
    # Trading Configuration (Fixed Parameters)
    base_interval: str = Field(
        default="1m",
        description="Base interval for data collection (fixed to 1m, do not modify)"
    )
    symbols: Union[str, List[str]] = Field(
        default="BTCUSDT,ETHUSDT",
        description="Trading symbols to monitor (comma-separated string or list)"
    )
    aggregate_intervals: Union[str, List[str]] = Field(
        default="3m,5m,15m,30m,1h,4h,1d",
        description="Target intervals for automatic aggregation (comma-separated string or list)"
    )
    websocket_intervals: Union[str, List[str]] = Field(
        default="3m,5m,15m",
        description="All intervals to subscribe via WebSocket (comma-separated string or list)"
    )
    stats_output_file: str = Field(
        default="/tmp/kline_stats.json",
        description="Statistics output file path"
    )
    
    # WebSocket Configuration
    websocket_chunk_size: int = Field(
        default=20,
        ge=1,
        le=100,
        description="Number of symbols per WebSocket connection"
    )
    max_retries: int = Field(
        default=5,
        ge=1,
        le=20,
        description="Maximum retry attempts for failed operations"
    )
    rate_limit_retry_delay: int = Field(
        default=60,
        ge=1,
        le=300,
        description="Default retry delay in seconds for rate limit (429) errors"
    )
    
    # Logging Configuration
    log_level: str = Field(
        default="INFO",
        description="Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)"
    )
    log_dir: str = Field(
        default="logs",
        description="Directory for log files"
    )
    log_file_max_size: str = Field(
        default="10MB",
        description="Maximum size for each log file before rotation"
    )
    log_file_backup_count: int = Field(
        default=5,
        ge=1,
        le=20,
        description="Number of backup log files to keep"
    )
    log_console_level: str = Field(
        default="ERROR",
        description="Console logging level (only errors and above shown in console)"
    )
    log_file_level: str = Field(
        default="DEBUG",
        description="File logging level (detailed logs in files)"
    )
    log_enable_file: bool = Field(
        default=True,
        description="Enable file logging"
    )
    log_enable_console: bool = Field(
        default=True,
        description="Enable console logging"
    )
    
    # Application Environment Configuration
    environment: str = Field(
        default="development",
        description="Application environment (development, production, test)"
    )
    debug: bool = Field(
        default=False,
        description="Enable debug mode"
    )
    
    # Exchange API Configuration
    exchange_api_base_url: str = Field(
        default="https://fapi.binance.com",
        description="Exchange API base URL"
    )
    rate_limit_requests: int = Field(
        default=10,
        ge=1,
        le=100,
        description="Rate limit requests per period"
    )
    rate_limit_period: int = Field(
        default=1,
        ge=1,
        le=60,
        description="Rate limit period in seconds"
    )
    
    # WebSocket Configuration Extended
    websocket_base_url: str = Field(
        default="wss://fstream.binance.com",
        description="WebSocket base URL"
    )
    websocket_auto_reconnect: bool = Field(
        default=True,
        description="Enable automatic WebSocket reconnection"
    )
    websocket_max_reconnect_attempts: int = Field(
        default=5,
        ge=1,
        le=20,
        description="Maximum WebSocket reconnection attempts"
    )
    websocket_reconnect_delay_seconds: int = Field(
        default=5,
        ge=1,
        le=60,
        description="Delay between reconnection attempts in seconds"
    )
    
    # Service Configuration
    historical_batch_size: int = Field(
        default=1000,
        ge=1,
        le=1500,
        description="Batch size for historical data fetching"
    )
    historical_rate_limit_delay: float = Field(
        default=0.1,
        ge=0.0,
        le=10.0,
        description="Rate limit delay for historical data requests"
    )
    aggregator_max_concurrent: int = Field(
        default=3,
        ge=1,
        le=10,
        description="Maximum concurrent aggregation operations"
    )
    aggregator_batch_size: int = Field(
        default=1000,
        ge=1,
        le=5000,
        description="Batch size for aggregation operations"
    )
    stream_buffer_size: int = Field(
        default=1000,
        ge=10,
        le=10000,
        description="Stream service buffer size"
    )
    stream_flush_interval_ms: int = Field(
        default=1000,
        ge=10,
        le=60000,
        description="Interval in milliseconds to flush WebSocket stream buffer"
    )

    # Startup HTTP backfill and WS gating configuration
    startup_backfill_enabled: bool = Field(
        default=True,
        description="Enable HTTP historical synchronization on startup"
    )
    startup_backfill_delete_tail: int = Field(
        default=100,
        ge=0,
        le=10000,
        description="Delete last N klines before HTTP sync to avoid dirty data"
    )
    startup_backfill_concurrency: int = Field(
        default=5,
        ge=1,
        le=20,
        description="Concurrency for symbol/interval sync tasks"
    )
    startup_backfill_default_window_minutes: int = Field(
        default=1440,
        ge=1,
        le=60*24*30,
        description="Fallback window (minutes) when no local data exists"
    )
    startup_backfill_target_klines: int = Field(
        default=1000,
        ge=1,
        le=100000,
        description="Number of K-lines to fetch per iteration (total = target_klines * iterations)"
    )
    startup_backfill_iterations: int = Field(
        default=5,
        ge=1,
        le=10000,
        description="Number of iterations to fetch historical data (total K-lines = target_klines * iterations)"
    )
    
    # API Configuration
    api_host: str = Field(
        default="0.0.0.0",
        description="API server host"
    )
    api_port: int = Field(
        default=8000,
        ge=1,
        le=65535,
        description="API server port"
    )
    api_cors_origins: Union[str, List[str]] = Field(
        default="*",
        description="CORS allowed origins (comma-separated string or list)"
    )
    api_static_path: str = Field(
        default="web",
        description="Path to static files directory"
    )
    
    @field_validator('base_interval', mode='after')
    @classmethod
    def validate_base_interval(cls, v: str) -> str:
        """Validate that base_interval is fixed to 1m."""
        if v != "1m":
            raise ValueError("base_interval is fixed to '1m' and cannot be modified")
        return v
    
    @field_validator('symbols', mode='before')
    @classmethod
    def parse_symbols(cls, v: Any) -> List[str]:
        """Parse comma-separated symbols from environment variable."""
        if isinstance(v, str):
            # Handle empty string or whitespace-only strings
            if not v.strip():
                return ["BTCUSDT", "ETHUSDT"]  # Default values
            return [sym.strip().upper() for sym in v.split(',') if sym.strip()]
        if isinstance(v, list):
            return [str(sym).strip().upper() for sym in v if str(sym).strip()]
        return ["BTCUSDT", "ETHUSDT"]  # Fallback default
    
    @field_validator('aggregate_intervals', mode='before')
    @classmethod
    def parse_aggregate_intervals(cls, v: Any) -> List[str]:
        """Parse comma-separated aggregate intervals from environment variable."""
        if isinstance(v, str):
            # Handle empty string or whitespace-only strings
            if not v.strip():
                return ["3m", "5m", "15m", "30m", "1h", "4h", "1d"]  # Default values
            return [interval.strip() for interval in v.split(',') if interval.strip()]
        if isinstance(v, list):
            return [str(interval).strip() for interval in v if str(interval).strip()]
        return ["3m", "5m", "15m", "30m", "1h", "4h", "1d"]  # Fallback default
    
    @field_validator('websocket_intervals', mode='before')
    @classmethod
    def parse_websocket_intervals(cls, v: Any) -> List[str]:
        """Parse comma-separated WebSocket intervals from environment variable."""
        if isinstance(v, str):
            # Handle empty string or whitespace-only strings
            if not v.strip():
                return ["1m", "3m", "5m", "15m", "30m", "1h", "2h", "4h", "6h", "8h", "12h", "1d"]  # Default values
            return [interval.strip() for interval in v.split(',') if interval.strip()]
        if isinstance(v, list):
            return [str(interval).strip() for interval in v if str(interval).strip()]
        return ["1m", "3m", "5m", "15m", "30m", "1h", "2h", "4h", "6h", "8h", "12h", "1d"]  # Fallback default
    
    @field_validator('log_level', 'log_console_level', 'log_file_level', mode='after')
    @classmethod
    def validate_log_level(cls, v: str) -> str:
        """Validate log level is one of the supported values."""
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'log_level must be one of: {valid_levels}')
        return v.upper()
    
    def get_redis_url(self) -> str:
        """Generate Redis connection URL."""
        if self.redis_password:
            return f"redis://:{self.redis_password}@{self.redis_host}:{self.redis_port}/{self.redis_db}"
        return f"redis://{self.redis_host}:{self.redis_port}/{self.redis_db}"
    
    def get_websocket_url(self, streams: List[str]) -> str:
        """Generate WebSocket URL for given streams."""
        stream_params = '/'.join(streams)
        return f"{self.base_url}/stream?streams={stream_params}"
    
    @property
    def symbols_list(self) -> List[str]:
        """Get symbols as list."""
        return self.symbols if isinstance(self.symbols, list) else self.parse_symbols(self.symbols)
    
    @property
    def aggregate_intervals_list(self) -> List[str]:
        """Get aggregate intervals as list."""
        return self.aggregate_intervals if isinstance(self.aggregate_intervals, list) else self.parse_aggregate_intervals(self.aggregate_intervals)
    
    @property
    def websocket_intervals_list(self) -> List[str]:
        """Get WebSocket intervals as list."""
        return self.websocket_intervals if isinstance(self.websocket_intervals, list) else self.parse_websocket_intervals(self.websocket_intervals)
    
    @property
    def api_cors_origins_list(self) -> List[str]:
        """Get CORS origins as list."""
        if isinstance(self.api_cors_origins, list):
            return self.api_cors_origins
        if self.api_cors_origins == "*":
            return ["*"]
        return [origin.strip() for origin in self.api_cors_origins.split(',') if origin.strip()]


# Global settings instance
settings = Settings()